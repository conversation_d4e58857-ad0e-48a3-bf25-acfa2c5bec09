<template>
  <div class="login_page">
    <div class="left_img">
      <div class="imgs">
        <img :src="login_icon" alt="" />
        <div class="info">
          <span>《蓝海译通隐私权政策》</span>
          <span>《蓝海译通客户权益保障承诺书》</span>
        </div>
        <div class="copy">© 2025 蓝海译通 Agency.All rights reserved.</div>
      </div>
    </div>
    <div class="login">
      <div class="sys_name">
        <img :src="logo" alt="" />
        <span>蓝海译通</span>
      </div>
      <div class="welcome_name">
        <h2>Hi, 欢迎来到蓝海译通!</h2>
        <p>聆听全球声音，翻译多种语言</p>
      </div>
      <div class="formData">
        <el-form>
          <div class="input_tit">手机号</div>
          <div class="input">
            <img :src="icon1" alt="" />
            <el-input v-model="formData.username" placeholder="请输入手机号" />
          </div>
          <div class="input_tit">密码</div>
          <div class="input">
            <img :src="icon2" alt="" />
            <el-input type="password" v-model="formData.password" placeholder="请输入密码" />
          </div>
          <div class="remarks">
            <div class="agree">
              点击登录，表示您接受以下 <span @click="isAgreement = true">用户协议</span>
            </div>
            <!-- <div class="forget"><span>忘记密码</span></div> -->
          </div>
          <el-form-item>
            <el-button class="btn" type="primary" @click="loginFun">登录</el-button>
          </el-form-item>
        </el-form>
        <div class="forget">
          没有账号？请联系客服&nbsp;<span style="color: rgb(85, 119, 255);">400-666-9515</span>
        </div>
        <div class="rights">© 蓝海译通. All rights reserved.</div>
      </div>
    </div>
    <div class="agreement" v-if="isAgreement">
      <div class="cont">
        <h2>
          <span>用户协议</span>
          <div class="close" @click="isAgreement = false">
            <img :src="icon54" alt="" />
          </div>
        </h2>
        <div class="info" v-html="siteInfoIndex.user_agreement"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineProps, toRaw } from 'vue'
import { ElMessage } from 'element-plus'
// 引入接口
import { login } from '../../api/login.js'
import { siteInfoSql } from '../../api/account.js'
// 导入router
import { useRouter } from 'vue-router' // 导入 useRouter
const router = useRouter() // 获取路由实例
// 获取pinia中的翻译配置
import { useTabStore } from '../../store/index.js'
const store = useTabStore()
// 导入本地图片
const login_icon = new URL('../../assets/img/login_icon.svg', import.meta.url).href
const logo = new URL('../../assets/img/logo.svg', import.meta.url).href
const icon1 = new URL('../../assets/img/phone.svg', import.meta.url).href
const icon2 = new URL('../../assets/img/icon2.svg', import.meta.url).href
const icon54 = new URL('../../assets/img/icon54.png', import.meta.url).href
// 定义登录信息
const formData = ref({})
const isAgreement = ref(false)
// 自动填充上一次登录的账户密码
const autoComplateInput = () => {
  try {
    const pre = localStorage.getItem("preUsers")
    if (pre) {
      let { phone, password } = JSON.parse(pre);
      formData.value.username = phone
      formData.value.password = password
    }
  } catch (error) {

  }
}
autoComplateInput()
// 登录方法
const loginFun = () => {
  let list = toRaw(store.tabList)
  let winList = []
  list.forEach(item => {
    winList.push(...item.lists)
  })
  let ids = []
  winList.forEach(item => {
    let id = item.platform + '/' + item.id
    ids.push(id)
  })
  
  login(formData.value).then((response) => {
    if (response.code == 1) {
      ElMessage({
        showClose: true,
        message: '登录成功',
        type: 'success'
      })


      // 如果登录的账号和上一个不同
      let account = localStorage.getItem('account')
      if(account !== formData.value.username){
        window.electron.ipcRenderer.send('clearS')
        window.electron.ipcRenderer.send('deleteView', ids.join(','))
      }

      localStorage.setItem("preUsers", JSON.stringify({
        phone: formData.value.username,
        password: formData.value.password,
      }))
      setTimeout(() => {
        localStorage.setItem('token', response.data.accessToken)
        localStorage.setItem('uuid', response.data.uuid)
        window.isExpire = false
        localStorage.setItem('realname', response.data.realname)
        localStorage.setItem('account', formData.value.username)
        window.electron.ipcRenderer.send('getUserLoginToken', { token: response.data.accessToken, uuid: response.data.uuid })
        setTimeout(function () {
          router.push('/')
        }, 500)
      }, 500)
    } else {
      ElMessage({
        showClose: true,
        message: response.msg,
        type: 'info'
      })
    }
  })
}

// 首页获取站点信息
const siteInfoIndex = ref({})
function siteInfo() {
  siteInfoSql({}).then((response) => {
    if (response.code == 1) {
      siteInfoIndex.value = response.data
    }
  })
}
siteInfo()

const keyDownLogin = (e) => {
  if (e.keyCode == 13) {
    loginFun()
  }
}

onMounted(() => {
  window.addEventListener('keydown', keyDownLogin) //监听用户回车事件
})
onUnmounted(() => {
  window.removeEventListener('keydown', keyDownLogin, false)
})
</script>

<style lang="scss">
@import url('../../assets/style/login.scss');
</style>
