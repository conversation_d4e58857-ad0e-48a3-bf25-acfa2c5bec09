<template>
	<div class="app_bar" :class="is_login ? 'systemBarLogin drawApp' : 'systemBar drawApp'">
		<div class="login_in">
			<div :class="is_message_show ? 'comp_btn' : 'comp_btn hide_message'">
				<div class="sys_name" @click="changeRouter('/')">
					<img :src="logo" alt="">
					<span>蓝海译通</span>
				</div>
				<div class="web_url" :title="`官网地址${'https://' + siteInfoIndex.website}`">
					<img :src="icon3" alt="" :draggable="false">
					<span>官网：
						<a :draggable="false" :href="'https://' + siteInfoIndex.website" target="_blank">{{
							siteInfoIndex.website }}</a>
					</span>
				</div>
				<div class="message">
					<img :src="icon4" alt="" :draggable="false">
					<span>通知:</span>
					<div class="scroll_text" @click="noticeClicFun">
						<p>{{ siteInfoIndex.notice }}</p>

					</div>
				</div>
			</div>
			<div :class="is_menu_word ? 'user_btn' : 'user_btn span_hide'">
				<div class="items" @click="toPath('/quick')">
					<img :draggable="false" :src="configSet.is_dark ? icon5_dark : icon5" alt="">
					<span>个人快捷回复</span>
				</div>
				<div class="items" @click="toPath('/all_translate')">
					<img :draggable="false" :src="configSet.is_dark ? icon6_dark : icon6" alt="">
					<span>聚合翻译</span>
				</div>
				<div class="items" @click="toPath('/sys_set')">
					<img :draggable="false" :src="configSet.is_dark ? icon7_dark : icon7" alt="">
					<span>全局设置</span>
				</div>
				<div class="items" @click="toPath('/contact')">
					<img :draggable="false" :src="configSet.is_dark ? icon8_dark : icon8" alt="">
					<span>联系我们</span>
				</div>
				<div class="items" @click="loginOut">
					<img :draggable="false" :src="configSet.is_dark ? icon9_dark : icon9" alt="">
					<span>退出登录</span>
				</div>
			</div>
		</div>
		<div class="sys_deal">
			<div class="out" @click="minApp">
				<img :draggable="false" :src="configSet.is_dark ? max_small_dark : max_small" alt="">
			</div>
			<div class="out" @click="screenAll">
				<img v-show="screenFull" :draggable="false" :src="configSet.is_dark ? max_big_dark : ScreenFull" alt="">
				<img v-show="!screenFull" :draggable="false" :src="configSet.is_dark ? max_big_dark : max_big" alt="">
			</div>
			<div class="out" @click="exitApp">
				<img :draggable="false" :src="configSet.is_dark ? clase_app_dark : clase_app" alt="">
			</div>
		</div>

	</div>
	<!-- 系统导航 -->
	<div v-if="!is_login">
		<TabarApp ref="tabarApp" softName='/'></TabarApp>
	</div>
	<div class="translate">
		<router-view v-slot="{ Component }">
			<component :is="Component" />
		</router-view>
	</div>
	<!-- <div class="ai_page" 
	v-if="show_ai_page"
    :style="elementStyle"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @mouseleave="onMouseUp"
	v-loading="loadingStatus"
	    element-loading-text="Loading..."
	    :element-loading-spinner="svg"
	    element-loading-svg-view-box="-10, -10, 50, 50"
	    element-loading-background="rgba(122, 122, 122, 0.8)"
	>
		<div class="tops" style="height: 60px;">
			<div class="tit">
				<img :src="ai" alt="">
			</div>
			<div class="btns">
				<img :src="clase_app" alt="" @click="show_ai_page = false;loadingStatus = true">
			</div>
		</div>
		<webview id='ai' :src="'https://deepai.lhyq360.com/app/ai/oauth?code='+user_token"></webview>
	</div> -->
	<!-- 系统更新设置 -->
	<div class="sys_update" v-if="show_update">
		<div class="is_update" v-if="!progressShow">
			<div class="close" @click="show_update = false">
				<img :src="close_up" alt="">
			</div>
			<div class="img">
				<img :src="update" alt="">
			</div>
			<div class="tit">
				<p>发现新版本</p>
				<small>{{ update_version }}</small>
			</div>
			<div class="des"></div>
			<div class="update_btn" @click="downExeBtn">立即更新</div>
		</div>
		<div class="is_update" v-if="progressShow">
			<div class="close" @click="show_update = false">
				<img :src="close_up" alt="">
			</div>
			<div class="img">
				<img :src="update" alt="">
			</div>
			<div class="tit">
				<p>发现新版本</p>
				<small>{{ update_version }}</small>
			</div>
			<div class="progress_num">{{ progress }}%</div>
			<div class="progress">
				<span :style="'width:' + progress + '%'"></span>
			</div>
			<div class="pregress_btn" @click="show_update = false">后台更新</div>
		</div>
	</div>
	<div class="shadow" v-if="show_update"></div>
	<div class="absolute top--6 w-100% flex justify-center bg-gray-200/50" v-if="loadingStore.loadingStatus">
		<Vue3Lottie class="" :animationData="loadingJson" :height="100" :width="100" @animCreated="handleAnimation" />
		<div class="absolute bottom-0 w-100% text-center font-700 color-indigo-500">
			正在执行中
		</div>
	</div>
</template>

<script setup>
import Versions from './components/Versions.vue'
import loadingJson from "@renderer/assets/lottie/loading.json"
import useLoadingStore from "@renderer/store/loading"
const loadingStore = useLoadingStore()
import { Vue3Lottie } from 'vue3-lottie';
import TabarApp from './components/tabarApp.vue'
import ScreenFull from "./assets/img/screenfull.svg"
import {
	ref,
	watch,
	onMounted,
	onUnmounted,
	provide,
	toRaw
} from 'vue'
import {
	batchOffLine
} from './api/account.js'
import {
	logOut
} from './api/login.js'
import {
	ElMessage, ElLoading,
	ElMessageBox
} from 'element-plus'
import {
	useRouter, useRoute
} from 'vue-router' // 导入 useRouter
import {
	siteInfoSql
} from "./api/account.js"

import { useTabStore } from './store/index.js'
import { useTranslateConfigStore } from './store/translateConfig'
import { useEscKey } from './utils/hooks.js'

const tabStroe = useTabStore()
useEscKey(() => {
	window.electron.ipcRenderer.send("hideApp")
})
window.___isShowVersionCount = 0;
const showHideWord = ref(true)
const responseFromMain = ref(null)
const router = useRouter() // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 当前更新进度
const progress = ref(0)
// 推出批量下线账号
const screenFull = ref(false)
const configSet = ref({ is_dark: false }) // 配置信息
// 导入图片
const max_small = new URL('./assets/img/max_small.svg', import.meta.url).href;
const max_big = new URL('./assets/img/max_big.svg', import.meta.url).href;
const clase_app = new URL('./assets/img/clsoe_app.svg', import.meta.url).href;
const icon5 = new URL('./assets/img/icon5.svg', import.meta.url).href;
const icon6 = new URL('./assets/img/icon6.svg', import.meta.url).href;
const icon7 = new URL('./assets/img/icon7.svg', import.meta.url).href;
const icon8 = new URL('./assets/img/icon8.svg', import.meta.url).href;
const icon9 = new URL('./assets/img/icon9.svg', import.meta.url).href;


const max_small_dark = new URL('./assets/img/dark/max_small.svg', import.meta.url).href;
const max_big_dark = new URL('./assets/img/dark/max_big.svg', import.meta.url).href;
const clase_app_dark = new URL('./assets/img/dark/clsoe_app.svg', import.meta.url).href;
const icon5_dark = new URL('./assets/img/dark/icon5.svg', import.meta.url).href;
const icon6_dark = new URL('./assets/img/dark/icon6.svg', import.meta.url).href;
const icon7_dark = new URL('./assets/img/dark/icon7.svg', import.meta.url).href;
const icon8_dark = new URL('./assets/img/dark/icon8.svg', import.meta.url).href;
const icon9_dark = new URL('./assets/img/dark/icon9.svg', import.meta.url).href;



const logo = new URL('./assets/img/logo.svg', import.meta.url).href;
const icon3 = new URL('./assets/img/icon3.svg', import.meta.url).href;
const icon4 = new URL('./assets/img/icon4.svg', import.meta.url).href;
const update = new URL('./assets/img/update.svg', import.meta.url).href;
const close_up = new URL('./assets/img/close_up.svg', import.meta.url).href;
const ai = new URL('./assets/img/ai.jpg', import.meta.url).href;
const loadingStatus = ref(true)

const tabarApp = ref()

provide("getTabList", () => tabarApp)

// const show_ai_page = ref(false)
// const show_ai = ref(false)
// const user_token = ref(localStorage.getItem('token')?localStorage.getItem('token'):'')

// watch(
// 	() => route.query,
// 	(newParams, oldParams) => {
// 		// 设置左侧是否显示
// 		// if (newParams.chat == 'slide') {
// 		// 	showHideWord.value = newParams.type == 'true' ? true : false
// 		// }
// 		// if(newParams.chat == 'show_ai'){
// 		// 	show_ai_page.value = true
// 		// 	setTimeout(function(){
// 		// 		loadingStatus.value = false
// 		// 	},1500)
// 		// }
// 	},
// 	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
// );
// 拖拽样式 ==================================================
const width = ref(900)
const height = ref(700)
const isResizing = ref(false)
const isDragging = ref(false)
const offsetX = ref(0)
const offsetY = ref(0)
const startX = ref(0)
const startY = ref(0)
const initialWidth = ref(900)
const initialHeight = ref(700)
const elementStyle = ref({
	width: width.value + 'px',
	height: height.value + 'px',
	transform: `translate(${offsetX.value}px, ${offsetY.value}px)`,
	cursor: isResizing.value ? 'se-resize' : 'move',
})

const onMouseDown = (event) => {
	console.log(event, 'eventeventevent')
	startX.value = event.clientX - offsetX.value;
	startY.value = event.clientY - offsetY.value;
	isDragging.value = true;

	// 检查是否在元素的右下角附近点击以开始缩放
	const rect = event.target.getBoundingClientRect();
	console.log(rect, 'rectrectrect')
	if (
		event.clientX > rect.right - 40 &&
		event.clientY > rect.bottom - 40
	) {
		isResizing.value = true;
		// isDragging.value = false;
		initialWidth.value = width.value;
		initialHeight.value = height.value;
	} else {
		isResizing.value = false;
		// isDragging.value = true;
	}
}
const onMouseMove = (event) => {
	console.log('eventeventevent', isDragging.value, isResizing.value)
	if (isDragging.value) {
		offsetX.value = event.clientX - startX.value;
		offsetY.value = event.clientY - startY.value;
	}

	if (isResizing.value) {
		console.log('event.clientX - startX.value', event.clientX - startX.value)
		width.value = initialWidth.value + (event.clientX - startX.value);
		height.value = initialHeight.value + (event.clientY - startY.value);
		// 确保宽度和高度不小于最小值
		width.value = Math.max(width.value, 50);
		height.value = Math.max(height.value, 50);
	}

	elementStyle.value = {
		width: width.value + 'px',
		height: height.value + 'px',
		transform: `translate(${offsetX.value}px, ${offsetY.value}px)`,
		cursor: isResizing.value ? 'se-resize' : 'move',
	}
}
const onMouseUp = () => {
	isDragging.value = false;
	isResizing.value = false;
}



// ==============================================

// 推出批量下线账号
const screenAll = () => {
	screenFull.value = !screenFull.value
	window.electron.ipcRenderer.send('sendToMainSySOver', screenFull.value)
}
// 关闭App
const exitApp = async () => {
try {
	
	configSet.value = JSON.parse(localStorage.getItem('configSet'))
	let isCloseApp = configSet.value.win
	if (isCloseApp) {
		await window.electron.ipcRenderer.invoke('quitApp')
		return
	} else {
		if (await window.electron.ipcRenderer.invoke('confirmQuit')) {
			batchOffLine({})
			window.electron.ipcRenderer.invoke('quitApp')
		}
	}
} catch (error) {
	console.log(error)
	await window.electron.ipcRenderer.invoke('quitApp')
}
}
// 最小化App
const minApp = () => {
	window.electron.ipcRenderer.send('sendToSySMin')
}
// 推出登录
async function loginOut(orderNumber) {
	await batchOffLine({})
	logOut({}).then(async response => {

		if (response.code == 1) {
			// 删除本地存储对话
			ElMessage({
				showClose: true,
				message: '退出账号成功',
				type: 'success'
			})

			window.electron.ipcRenderer.send('sysSet', false, "message")

			router.push({
				path: '/login',
				query: { 'chat': 'loginout' }
			})

		} else {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'warn'
			})

		}
	})
}
// 首页获取站点信息
const siteInfoIndex = ref({})
function siteInfo() {
	siteInfoSql({}).then(response => {
		if (response.code == 1) {
			siteInfoIndex.value = response.data
		}
	})
}
siteInfo()
const toPath = (url) => {
	router.push({
		path: url
	})
}
const open_webview = () => {
	const isWebView = document.querySelector('#ai');
	if (isWebView) {
		isWebView.addEventListener("did-finish-load", () => {
			// 隐藏加载
			loadingStatus.value = false
		})
	} else {
		setTimeout(function () {
			open_webview()
		}, 1000)
	}

}

const update_version = ref() // 更新版本
const show_update = ref(false) // 是否显示更新弹窗
const progressShow = ref(false) // 是否显示下载进度
const downExeBtn = () => {
	progressShow.value = true
	window.electron.ipcRenderer.send('now_down_exe')
}

// 监听未读消息

const messageCount = ref()
const onUnReadMessageChange = () => {
	electron.ipcRenderer.on("unReadMessageCountChange", (_event, params) => {
		console.log(params);

	})
}
onUnReadMessageChange()

onMounted(() => {
	if (localStorage.getItem('configSet')) {
		configSet.value = JSON.parse(localStorage.getItem('configSet'))
		window.electron.ipcRenderer.send('sysSet', configSet.value.win, 'win')
		document.documentElement.setAttribute('data-theme', configSet.value.is_dark ? 'dark' : 'light')
		window.electron.ipcRenderer.send('sysSet', configSet.value.message, "message")
		window.electron.ipcRenderer.send('sysSet', configSet.value.ai, "ai")
		window.electron.ipcRenderer.send('sysSet', toRaw(configSet.value.aiTimeRang), "aiTimeRang")
	} else {
		configSet.value.is_dark = false
		window.electron.ipcRenderer.send('sysSet', false, 'win')
		window.electron.ipcRenderer.send('sysSet', configSet.value.ai || false, "ai")
		document.documentElement.setAttribute('data-theme', configSet.value.is_dark ? 'dark' : 'light')
	}

	// 监听主进程发送过来的是否改变暗黑模式
	window.electron.ipcRenderer.on("reloadIconUrl", (event, data) => {
		configSet.value.is_dark = data
	});

	open_webview()
	// 处理顶部公告滚动
	setNoticeScroll()
	window.electron.ipcRenderer.on("cacleFullScreen", (event, data) => {
		if (!data) {
			handleResizeApp()
		}
	});

	// 监听有新版本 update-available
	window.electron.ipcRenderer.on("update-available", (event, data) => {
		console.log('data', data)
		update_version.value = data
		show_update.value = true
	});
	// 监听下载进度
	window.electron.ipcRenderer.on("download-progress-number", (event, data, numbers) => {
		console.log('download-progress-number', data)
		console.log('download-progress-number', numbers)
		progress.value = data.toFixed(2)
	});
	// 监听没有课更新的版本消息
	window.electron.ipcRenderer.on("no-update-version", (event, is_hand_btn) => {
		if (is_hand_btn) {
			ElMessage({
				showClose: true,
				message: "当前已是最新版本",
				type: 'warning'
			})
		}
	});

	// // 监测系统窗口 是否移动窗口1
	// const draggableElement = document.querySelector('.drawApp');
	// let isDragging = false;
	// let lastX, lastY;

	// draggableElement.addEventListener('mousedown', (event) => {
	// 	isDragging = true;
	// 	lastX = event.screenX;
	// 	lastY = event.screenY;
	// });

	// window.addEventListener('mousemove', (event) => {
	// 	if (isDragging) {
	// 		const deltaX = event.screenX - lastX;
	// 		const deltaY = event.screenY - lastY;
	// 		window.electron.ipcRenderer.send('drag-start', {
	// 			x: deltaX,
	// 			y: deltaY
	// 		});
	// 		lastX = event.screenX;
	// 		lastY = event.screenY;
	// 	}
	// });
	// window.addEventListener('mouseup', () => {
	// 	isDragging = false;
	// });

	// window.addEventListener('mouseleave', () => {
	// 	isDragging = false;
	// });

	window.addEventListener('resize', handleResizeApp);
	setTimeout(function () {
		handleResizeApp()
	}, 1000)

	// 全局设置
	window.electron.ipcRenderer.send('sysSet', localStorage.getItem('configSet') ? JSON.parse(localStorage.getItem('configSet')).win : '')
})
// 监听窗口大小变化

const setIntervalFun = ref()
const is_menu_word = ref(false) // 是否显示导航按钮
const is_message_show = ref(true) // 是否显示公告信息
const handleResizeApp = () => {
	let width = window.innerWidth;
	let height = window.innerHeight;
	// 小于1965的时候 隐藏文字
	if (width <= 1450) {
		is_message_show.value = false
		is_menu_word.value = false
	} else if (width > 1450 && width < 1970) {
		is_message_show.value = true
		is_menu_word.value = false
	} else {
		is_menu_word.value = true
		is_message_show.value = true
	}
};
onUnmounted(() => {
	clearInterval(setIntervalFun.value)
	window.removeEventListener('resize', handleResizeApp);
});
// 获取token是否存在，token存在即进入系统 不存在 在登录页面
const is_login = ref(true)
const sysLogin = () => {
	let token = localStorage.getItem('token')
	if (!token || token == '') {
		router.push('/login')
	}
}
// 使用 afterEach 导航守卫
router.afterEach((to, from) => {
	if (to.path.indexOf('/index.html') !== -1) {
		router.push('/')
	}
	if (to.path == '/login') {
		is_login.value = true
	} else {
		is_login.value = false
	}
});
// 跳转页面
const changeRouter = (url) => {
	router.push(url)
}
setInterval(function () {
	sysLogin()
}, 3000)
// 处理顶部公告滚动
const setNoticeScroll = () => {
	clearInterval(setIntervalFun.value)
	let scrollDom = document.querySelector('.scroll_text')
	let parentWidth = scrollDom.offsetWidth
	if (scrollDom && parentWidth > 0) {
		let scrollDomP = document.querySelector('.scroll_text p')
		let sonWidth = scrollDomP.offsetWidth
		// 取到元素 去判断大小 是否需要滚动
		if (parentWidth >= sonWidth) {
			clearInterval(setIntervalFun.value)
		} else {
			// 拿到网页元素
			let html_dom = scrollDomP.outerHTML
			scrollDom.innerHTML = html_dom + html_dom
			// 获取两倍dom后，重新获取dom
			let dulDom = document.querySelectorAll('.scroll_text p')
			dulDom.forEach(function (sonDom, index) {
				if (index == 0) {
					sonDom.style.left = 0
				}
				if (index == 1) {
					sonDom.style.left = sonWidth + 'px'
				}
			})
			setIntervalFun.value = setInterval(function () {
				// 获取滚动元素内容
				let dulDomScroll = document.querySelectorAll('.scroll_text p')
				dulDomScroll.forEach(function (sonDom, index) {

					if (sonDom.offsetLeft + sonWidth <= 0) {
						sonDom.style.left = sonWidth + 'px'
					} else {
						sonDom.style.left = (sonDom.offsetLeft - 2) + 'px'
					}
				})

			}, 50)
		}

	} else {
		// 没有取到元素值的时候
		setTimeout(function () {
			setNoticeScroll()
		}, 1000)
	}

}
const noticeClicFun = () => {
	setNoticeScroll()
}
</script>



<style lang="scss" scoped>
.app_bar {
	-webkit-app-region: drag;

	.login_in {

		.web_url,
		.message,
		.items {
			-webkit-app-region: no-drag;
		}
	}

	.sys_deal {
		.out {
			-webkit-app-region: no-drag;
		}
	}
}
</style>
<style>
.sys_left {
	width: 192px;
	height: calc(100vh - 90px);
	left: 10px;
	top: 80px;
	border-radius: 10px;
	position: fixed;
	background-color: var(--bg-color-cont);
	transition: all 0.3s;
	z-index: 98;
}

.sys_left_b {
	width: 85px;
}

.small_tab {
	margin-left: 102px !important;
	width: calc(100% - 112px) !important;

	.webCont {
		width: calc(100vw - 110px) !important;
	}
}

.ai_page {

	background-color: #fff;
	box-shadow: 0 0 10px #ccc;
	border-radius: 20px;
	border: 1px solid #eee;
	overflow: hidden;
	position: absolute;
	background-color: lightblue;
	user-select: none;
	/* 防止文本选择 */
	z-index: 9999;
}

.ai_page #ai {
	width: 100%;
	height: 100%;
	background-color: #fff;
}

.ai_page .tops {
	height: 60px;
	line-height: 60px;
	display: flex;
	align-items: center;
	position: relative;
	justify-content: space-between;
	border-bottom: 1px solid #eee;
	background: #fff url('../src/assets/img/ai.jpg') no-repeate left center;
}

.ai_page .tops .tit {
	font-size: 24px;
	color: #333;
	width: 100%;
	height: 60px;

	img {
		width: 100%;
		height: 60px;
	}
}

.ai_page .tops .btns {
	display: flex;
	position: absolute;
	right: 20px;
	align-items: center;
}

.sys_update {
	position: fixed;
	z-index: 10000;
	background-color: #f00;
	left: calc(50% - 330px);
	top: calc(45% - 135px);
	background-color: #F2F4F9;
	width: 640px;
	padding: 20px 30px;
	border-radius: 20px;

	.close {
		position: absolute;
		right: 15px;
	}

	.img {
		text-align: center;

		img {
			width: 110px;
			height: 108px;
		}
	}

	.tit {
		text-align: center;

		p {
			font-family: PingFang HK;
			font-weight: 600;
			font-size: 20px;
			line-height: 20px;
			letter-spacing: 0px;
			text-align: center;
			color: #15141F;
		}

		small {
			font-family: ABeeZee;
			font-weight: 400;
			font-size: 14px;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: center;
			color: #7E7E81;
			display: block;
			padding: 10px 0;
		}
	}

	.des {
		font-family: ABeeZee;
		font-weight: 400;
		font-size: 16px;
		line-height: 26px;
		letter-spacing: 0px;
		color: #15141F;
	}

	.update_btn {
		width: 60%;
		background-color: #4263EB;
		height: 53px;
		border-radius: 26.5px;
		text-align: center;
		line-height: 53px;
		margin: 30px auto 0;
	}

	.progress_num {
		font-family: ABeeZee;
		font-weight: 400;
		font-size: 20px;
		line-height: 20px;
		letter-spacing: 0px;
		text-align: center;
		color: #4263EB;
		margin-top: 35px;
		padding-bottom: 20px;
	}

	.progress {
		width: 100%;
		height: 16px;
		margin-bottom: 35px;
		background-color: #fff;
		border-radius: 40px;

		span {
			display: block;
			width: 1%;
			background-color: #4263EB;
			height: 16px;
			border-radius: 40px;
		}
	}

	.pregress_btn {
		font-family: Abhaya Libre;
		font-weight: 400;
		font-size: 16px;
		line-height: 16px;
		letter-spacing: 0px;
		text-align: right;
		color: #999999;
	}
}
</style>