import { net } from 'electron';
export const googleEngineMap = new Map([
  ["googlexjp", "https://www.google.com.sg"],
  ["googlehk", "https://www.google.com.hk"],
  ["googlehw", "https://www.google.com"],
]);


function parseGoogleAsyncHTML(html) {
  const extract = (id) => {
    const regex = new RegExp(`<span[^>]*id="${id}">(.*?)<\\/span>`);
    const match = html.match(regex);
    return match?.[1] ?? null;
  };

  console.log(html, extract('tw-answ-target-text'));

  return {
    sourceText: extract('tw-answ-source-text'),
    translatedText: extract('tw-answ-target-text'),
    detectedLangCode: extract('tw-answ-detected-sl'),
    romanization: extract('tw-answ-source-romanization'),
    sourceSpelling: extract('tw-answ-spelling'),
    langName: extract('tw-answ-language-detected'),
  };
}


const langCodeMap = {
  en: 'en',
  zh: 'zh-CN',
  de: 'de',
  th: 'th',
  pt: 'pt',
  fra: 'fr',
  spa: 'es',
  "ara": 'ar',
  id: 'id',
};

/**
 * @param {Object} params
 * @param {string} params.text
 * @param {string} params.fromLang
 * @param {string} params.toLang
 * @param {string} params.engineCode
 * @returns {Promise<{ code: 1, data: { result: string } } | null>}
 */
export function googleAsyncTranslate({ text, fromLang, toLang, engineCode = 'googlehk' }) {
  return new Promise((resolve, reject) => {
    const baseURL = googleEngineMap.get(engineCode);
    if (!baseURL) {
      console.error(`无效的 engineCode: ${engineCode}`);
      return resolve(null);
    }

    const id = Date.now();

    const mapLang = (code) => langCodeMap[code] || code; // fallback to original if not found

    const postData = new URLSearchParams({
      async: `translate,sl:${mapLang(fromLang)},tl:${mapLang(toLang)},st:${encodeURIComponent(text)},id:${id},qc:true,ac:true,_id:tw-async-translate,_pms:s,_fmt:pc`,
    }).toString();


    const request = net.request({
      method: 'POST',
      url: `${baseURL}/async/translate`,
    });

    request.setHeader('Content-Type', 'application/x-www-form-urlencoded;charset=UTF-8');
    request.setHeader('Referer', baseURL);
    request.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36');
    request.setHeader('Sec-CH-UA', '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"');
    request.setHeader('Sec-CH-UA-Mobile', '?0');
    request.setHeader('Sec-CH-UA-Platform', '"Windows"');
    request.setHeader('Sec-CH-UA-Arch', '"x86"');
    request.setHeader('Sec-CH-UA-Full-Version', '"138.0.7204.158"');

    let responseBody = '';

    request.on('response', (response) => {
      response.on('data', (chunk) => {
        responseBody += chunk.toString();
      });

      response.on('end', () => {
        try {
          const htmlParse = parseGoogleAsyncHTML(responseBody);
          const translated = htmlParse.translatedText;
          console.log("😼google", htmlParse);

          resolve({
            code: translated ? 1 : 4001,
            data: {
              ...htmlParse,
              result: translated,
            },
          });
        } catch (err) {
          console.error('解析失败:', err.message);
          resolve(null);
        }
      });
    });

    request.on('error', (err) => {
      console.error('翻译请求失败:', err.message);
      resolve(null);
    });

    request.write(postData);
    request.end();
  });
}


