<template>
  <div
    @click="closeUpdateHistory"
    v-show="showUpdateHistory"
    class="position-fixed top-0 left-0 w-100vw h-100vh bg-white-100/10 flex justify-center items-center"
  >
    <div @click.stop="() => {}" class="changelog-container shadow-gray-300/50">
      <div ref="scrollEl" class="changelog os-host">
        <h1>更新日志</h1>
        <div class="close-btn" @click="closeUpdateHistory">关闭</div>
        <div
          v-for="(log, index) in changelogs"
          :key="index"
          class="version-block"
          :class="{ open: log.open }"
        >
          <div class="version-header" @click="toggleVersion(index)">
            <div class="version-title">{{ log.version }}</div>
            <div class="flex items-center justify-between">
              <span class="version-date">{{ log.date }}</span>
              <span class="toggle-icon inline-block ml-2" :class="{ 'rotate-40': log.open }"
                >▶</span
              >
            </div>
          </div>
          <div class="version-content transition-all">
            <ul>
              <li>{{ log.ver_intro }}</li>
              <!-- <li v-for="(item, idx) in log.changes" :key="idx">{{ item }}</li> -->
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { OverlayScrollbars } from 'overlayscrollbars'
import 'overlayscrollbars/overlayscrollbars.css'
import { getAppVersion, getAppUpdateHistoryVersion } from '../api/login.js'

const changelogs = ref([])

const showUpdateHistory = ref(false)

let timmer

async function init() {
  const count = sessionStorage.getItem('showHistory') || 0
  if (count > 0) {
    return
  }
  const v = await getAppVersion()
  console.log(v)
  if (v.code !== 1) {
    return
  }

  if (v.data.version === localStorage.getItem('version')) {
    return
  } else {

    const uh = await getAppUpdateHistoryVersion()
     showUpdateHistory.value = true
    if (uh.code === 1) {
      changelogs.value = uh.data
    }
    timmer = setTimeout(() => {
      sessionStorage.setItem('showHistory', 1)
    }, 2000)
  }
}
init()
const scrollEl = ref(null)
let osInstance = null

const closeUpdateHistory = () => {
  showUpdateHistory.value = false
}
function toggleVersion(index) {
  changelogs.value[index].open = !changelogs.value[index].open
  nextTick(() => {
    if (osInstance) {
      osInstance.update()
    }
  })
}

onMounted(() => {
  osInstance = OverlayScrollbars(scrollEl.value, {
    scrollbars: {
      autoHide: 'leave',
      theme: 'os-theme-dark'
    }
  })
})

onBeforeUnmount(() => {
  clearTimeout(timmer)
  if (osInstance) {
    osInstance.destroy()
    osInstance = null
  }
})
</script>

<style scoped>
:global(.os-theme-dark) {
  --os-handle-bg: #7c9bb4fa;
  --os-handle-bg-hover: #2881cafa;
  --os-handle-bg-active: #2881cafa;
}
h1 {
  text-align: center;
  margin-bottom: 10px;
  font-weight: bold;

  color: #0078d7;
}
.changelog {
  max-width: 700px;
  margin: 0 auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px 30px;
  min-height: 150px;
  max-height: 400px; /* 限制高度以出现滚动条 */
  overflow: hidden; /* 由 OverlayScrollbars 接管滚动 */
}
.version-block {
  border-bottom: 1px solid #ddd;
  padding: 15px 0;
}
.version-block:last-child {
  border-bottom: none;
}
.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.version-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #0078d7;
}
.version-date {
  font-size: 0.9rem;
  color: #666;
}
.version-content {
  margin-top: 10px;
  display: none;

  color: #444;
}
.version-content ul {
  padding-left: 20px;
  margin: 0;
}
.version-content ul li {
  margin-bottom: 6px;
  line-height: 1.4;
}
.version-block.open .version-content {
  display: block;
}
.toggle-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
  user-select: none;
  color: #0078d7;
}
.version-block.open .toggle-icon {
  transform: rotate(48deg);
}
.changelog-container {
  width: 40vw;
  height: 55vh;
  /* position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999; */
}
.close-btn {
  color: #bbbbbb;
  position: absolute;
  right: 30px;
  top: 20px;
  cursor: pointer;
}
</style>
