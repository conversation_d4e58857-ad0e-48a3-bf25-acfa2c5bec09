<template>
  <div class="user_set">
    <div :class="is_open ? 'set_cont set_cont_show' : 'set_cont'">
      <!-- // 翻译设置信息1-->
      <div class="translate_set" v-if="is_open_tab == 1">
        <h2>翻译设置</h2>
        <el-form ref="form" label-position="right" :model="translateSet" label-width="265px">
          <div class="select_info">
            <div class="label_tit">翻译引擎</div>
            <el-select
              v-model="translateSet.engineCode"
              @change="changeTranslateFun"
              placeholder="翻译引擎"
            >
              <el-option
                v-for="item in allEngine"
                :label="item.engine_name"
                :value="item.engine_code"
              ></el-option>
            </el-select>
          </div>
          <div class="select_info">
            <div class="label_tit">对方语言</div>
            <el-select disabled v-model="translateSet.gust_lang" placeholder="自动检测">
              <el-option label="自动检测" value=""></el-option>
              <el-option
                v-for="item in allLang"
                label="自动检测"
                :value="item.lang_code"
              ></el-option>
            </el-select>
          </div>
          <div class="select_info line">
            <div class="label_tit">接收信息翻译为</div>
            <el-select
              v-model="translateSet.recive_lang"
              @change="changeTranslateFun"
              placeholder="接收信息翻译为"
            >
              <el-option label="请选择接语言" value=""></el-option>
              <el-option
                v-for="item in allLang"
                :label="item.lang_name"
                :value="item.lang_code"
              ></el-option>
            </el-select>
          </div>
          <div class="select_info">
            <div class="label_tit">输入内容翻译后发送</div>
            <el-select
              v-model="translateSet.send_lang"
              @change="changeTranslateFun"
              placeholder="输入内容翻译后发送"
            >
              <el-option label="请选择接语言" value=""></el-option>
              <el-option
                v-for="item in allLang"
                :label="item.lang_name"
                :value="item.lang_code"
              ></el-option>
            </el-select>
          </div>
          <el-form-item label-width="auto">
            <div style="width: 100%; display: flex; justify-content: flex-end; align-items: center">
              <div style="white-space: nowrap; margin-right: 10px">
                {{ '自动翻译' }}
              </div>
              <el-switch @click="changeTranslateFun" v-model="translateSet.self"></el-switch>
            </div>
          </el-form-item>
          <el-form-item label-width="auto">
            <div style="width: 100%; display: flex; justify-content: flex-end; align-items: center">
              <div style="white-space: nowrap; margin-right: 10px">
                {{ '翻译后发送' }}
              </div>
              <el-switch @click="changeTranslateFun" v-model="translateSet.trans_over"></el-switch>
            </div>
          </el-form-item>
          <!-- <el-form-item label-width="auto">
            <div style="width: 100%; display: flex; justify-content: flex-end; align-items: center">
              <div style="white-space: nowrap; margin-right: 10px">
                {{ '自动翻译群消息' }}
              </div>
              <el-switch @click="changeTranslateFun" v-model="translateSet.self_many"></el-switch>
            </div>
          </el-form-item> -->
          <!-- <el-form-item label="登录前自动翻译历史信息">
            <el-switch
              @click="changeTranslateFun"
              v-model="translateSet.login_trans_his"
            ></el-switch>
          </el-form-item> -->
          <!-- <el-form-item label="发送时自动翻译中文">
						<el-switch @click="changeTranslateFun" v-model="translateSet.send_trans_zh"></el-switch>
					</el-form-item> -->
        </el-form>
      </div>
      <!-- 快捷回复 -->
      <div class="quick_reply" v-if="is_open_tab == 2">
        <div class="top">
          <div class="top_tit">
            <h2>快捷回复</h2>
            <img :src="icon39" alt="" class="cursor-pointer" @click="changeQuickType(quickType)" />
          </div>
          <img :src="icon40" alt="" />
        </div>
        <!-- // 快捷回复类型-->
        <div class="quick_type">
          <div :class="quickType == 1 ? 'item on' : 'item'" @click="changeQuickType(1)">个人</div>
          <div :class="quickType == 2 ? 'item on' : 'item'" @click="changeQuickType(2)">公共</div>
        </div>
        <!-- 发送方式sendType -->
        <div class="sendType">
          <div :class="sendType == 1 ? 'item on' : 'item'" @click="changeSendType(1)">
            <div class="icon">
              <img v-if="sendType == 1" :src="icon44" alt="" />
            </div>
            <span>原文发送</span>
          </div>
          <div :class="sendType == 2 ? 'item on' : 'item'" @click="changeSendType(2)">
            <div class="icon">
              <img v-if="sendType == 2" :src="icon44" alt="" />
            </div>
            <span>翻译后发送</span>
          </div>
        </div>
        <!-- 输入关键词 -->
        <div class="search">
          <div class="input">
            <input
              type="text"
              @input="searchInput"
              v-model="searchName"
              placeholder="请输入关键词"
            />
            <img :src="icon41" alt="" />
          </div>
        </div>
        <div class="empty_quick" v-if="quickReply.length == 0">
          <div class="empty flex flex-col justify-center">
            <img :src="icon45" alt="" />
            <p>暂未设置回复内容</p>
            <div>立即添加</div>
          </div>
        </div>
        <!-- 快捷回复数据 -->
        <div class="quick_list">
          <div class="items" v-for="(item, index) in quickReply">
            <div class="tit" @click="showOrCloseFun(index, -1)">
              <img :src="icon42" alt="" />
              <span>{{ item.name }}</span>
            </div>
            <div v-if="item.is_sel" class="item" v-for="(ite, ind) in item.quickReply">
              <div class="tit" @click="showOrCloseFun(index, ind)">
                <img :src="icon43" alt="" />
                <span>{{ ite.name }}</span>
              </div>
              <div v-if="ite.is_sel" class="quick_infos">
                <div class="infos_item" v-for="(it, is) in ite.text">
                  <div class="btn">
                    <div class="num">{{ is + 1 }}</div>
                    <div class="btn">
                      <p @click="sendClickFun('send', quickType == 1 ? it.message : it)">发送</p>
                      <p @click="sendClickFun('input', quickType == 1 ? it.message : it)">
                        输入框展示
                      </p>
                    </div>
                  </div>
                  <div class="quick_cont">
                    {{ quickType == 1 ? it.message : it }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 粉丝备注 -->
      <div class="fans_remark" v-if="is_open_tab == 3">
        <h2>粉丝备注</h2>
        <div class="fans_id">
          <p>客户</p>
          <span>{{ fansInfo.id }}</span>
        </div>
        <div class="base_info">
          <el-form ref="form" :model="fansInfo" label-width="265px">
            <h3>基本信息</h3>
            <el-form-item label="是否标记为客户">
              <el-switch
                v-model="fansInfo.is_customer"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-form-item>
            <div class="input_info input_info_name">
              <div class="label_tit"><span style="color: red">*</span>客户名</div>
              <el-input v-model="fansInfo.fans_name" placeholder="请输入客户名"></el-input>
            </div>
            <div class="input_info">
              <div class="label_tit"><span style="color: red">*</span>电话</div>
              <el-input v-model="fansInfo.fans_mobile" placeholder="请输入电话"></el-input>
            </div>
            <div class="input_info">
              <div class="label_tit">国家</div>
              <el-input v-model="fansInfo.country" placeholder="请输入国家"></el-input>
            </div>
            <div class="user_sex">
              <div class="label_tit">性别</div>
              <div class="sex_info">
                <div :class="sex == 1 ? 'item on' : 'item'" @click="cahngeSex(1)">
                  <p>
                    <img v-if="sex == 1" :src="icon44" alt="" />
                  </p>
                  <span>男</span>
                </div>
                <div :class="sex == 2 ? 'item on' : 'item'" @click="cahngeSex(2)">
                  <p>
                    <img v-if="sex == 2" :src="icon44" alt="" />
                  </p>
                  <span>女</span>
                </div>
              </div>
            </div>
            <h3>销售信息</h3>
            <div class="select_info">
              <div class="label_tit"><span style="color: red">*</span>客户等级</div>
              <el-select v-model="fansInfo.fans_level" placeholder="请选择客户等级">
                <el-option
                  v-for="item in fansConfig.fans_level"
                  :label="item.name"
                  :value="`${item.id}`"
                ></el-option>
              </el-select>
            </div>
            <div class="select_info">
              <div class="label_tit"><span style="color: red">*</span>订单阶段</div>
              <el-select v-model="fansInfo.order_stage" placeholder="请选择订单阶段">
                <el-option
                  v-for="item in fansConfig.order_stage"
                  :label="item.name"
                  :value="`${item.id}`"
                ></el-option>
              </el-select>
            </div>
            <div class="select_info">
              <div class="label_tit"><span style="color: red">*</span>销售阶段</div>
              <el-select v-model="fansInfo.sales_stage" placeholder="请选择销售阶段">
                <el-option
                  v-for="item in fansConfig.sales_stage"
                  :label="item.name"
                  :value="`${item.id}`"
                ></el-option>
              </el-select>
            </div>
            <div class="select_info">
              <div class="label_tit">社媒来源</div>
              <el-select v-model="fansInfo.fans_sources" placeholder="请选择社媒来源">
                <el-option
                  v-for="item in fansConfig.fans_sources"
                  :label="item.name"
                  :value="`${item.id}`"
                ></el-option>
              </el-select>
            </div>
            <!-- <div class="select_info">
							<div class="label_tit">自定义标签</div>
							<el-select v-model="fansInfo.region" placeholder="请输入标签">
								<el-option label="区域一" value="shanghai"></el-option>
								<el-option label="区域二" value="beijing"></el-option>
							</el-select>
						</div> -->
            <h3>公司信息</h3>
            <div class="input_info input_info_name">
              <div class="label_tit"><span style="color: red">*</span>公司名称</div>
              <el-input v-model="fansInfo.company" placeholder="请输入公司名称"></el-input>
            </div>
            <div class="input_info input_info_name">
              <div class="label_tit">职位</div>
              <el-input v-model="fansInfo.position" placeholder="请输入职位"></el-input>
            </div>
            <h3>备注</h3>
            <div class="input_info input_info_name">
              <div class="label_tit">备注</div>
              <el-input
                placeholder="请输入备注"
                style="height: 100px"
                type="textarea"
                v-model="fansInfo.remark"
              ></el-input>
            </div>
          </el-form>
        </div>
        <div class="save_fun" @click="updateFansInfo">保存</div>
      </div>
      <!-- 粉丝统计 -->
      <div class="fans_state" v-if="is_open_tab == 4">
        <h2>粉丝统计</h2>
        <div class="state_info">
          <h3>当前工单</h3>
          <div class="numbers">
            <p>当日置零后进粉总数：</p>
            <span>{{ fansStatias.new_fans_number ? fansStatias.new_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>当日置零后重粉总数：</p>
            <span>{{ fansStatias.repeat_fans_number ? fansStatias.repeat_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>工单重置后进粉总数：</p>
            <span>{{ fansStatias.all_fans_number ? fansStatias.all_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>工单重置后重粉总数：</p>
            <span>{{
              fansStatias.all_repeat_fans_number ? fansStatias.all_repeat_fans_number : 0
            }}</span>
          </div>
        </div>
        <div class="state_info">
          <h3>当前账号</h3>
          <div class="numbers">
            <p>当日置零后进粉总数：</p>
            <span>{{ fansRecord.news_fans_number ? fansRecord.news_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>当日置零后重粉总数：</p>
            <span>{{ fansRecord.repeat_fans_number ? fansRecord.repeat_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>新粉数：</p>
            <span>{{ fansRecord.all_fans_number ? fansRecord.all_fans_number : 0 }}</span>
          </div>
          <div class="numbers">
            <p>重粉数：</p>
            <span>{{
              fansRecord.all_repeat_fans_number ? fansRecord.all_repeat_fans_number : 0
            }}</span>
          </div>
          <div class="numbers">
            <p>全部粉丝数：</p>
            <span>{{ fansRecord.all_fans_number ? fansRecord.all_fans_number : 0 }}</span>
          </div>
        </div>
      </div>
      <!-- 自动回复 -->
      <div class="fans_state" v-if="is_open_tab == 7">
        <h2>自动回复</h2>
        <div class="switch_line">
          <span :class="autoReplySetInfo.isAutoReply ? 'open_auto_reply' : 'close_auto_reply'">{{
            `自动回复-${autoReplySetInfo.isAutoReply ? '已开启' : '已关闭'}`
          }}</span>
          <el-switch v-model="autoReplySetInfo.isAutoReply"></el-switch>
        </div>
        <div class="tips">
          注：开启监控群消息后，如果命中自动回复，会对发送的人打开一对一聊天窗口后再自动发送回复内容
        </div>
        <div v-for="(item, index) in autoReplySetInfo.autoReplySetList" style="margin-bottom: 10px">
          <div>
            <div style="display: flex; justify-content: center; align-items: center">
              <span style="font-size: 16px; font-weight: bold; margin-right: 5px; color: #081735"
                >回复-{{ index + 1 }}</span
              >
              <img
                :src="Delete"
                style="width: 15px; height: 15px; cursor: pointer"
                alt=""
                @click="deleteReply(index)"
              />
            </div>
            <div
              style="
                text-align: center;
                margin: 10px 0px;
                margin-top: 5px;
                font-size: 16px;
                color: #081735;
              "
            >
              自动回复内容
            </div>
            <div style="margin-bottom: 20px">
              <el-input
                v-model="item.replyContent"
                type="textarea"
                :autosize="{ minRows: 5, maxRows: 5 }"
                placeholder="自动回复的内容"
                :clearable="true"
                :rows="5"
              ></el-input>
            </div>
          </div>
          <div class="switch_line">
            <span style="font-size: 16px; color: #081735">同时监控群消息</span>
            <el-switch v-model="item.isSyncMonitoringGroup"></el-switch>
          </div>
          <div
            v-for="(rule, i) in item.monitoringKeywords"
            style="
              background-color: #f7f7f7;
              border-radius: 8px;
              padding: 15px 20px;
              margin-top: 20px;
            "
          >
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                margin-bottom: 20px;
              "
            >
              <span>监控关键词-{{ i + 1 }}</span>
              <img
                :src="Delete"
                title="删除监控关键词"
                style="width: 15px; height: 15px; cursor: pointer"
                alt=""
                @click="deleteRule(item.monitoringKeywords, i)"
              />
            </div>
            <el-input
              v-model="rule.keywords"
              :clearable="true"
              placeholder="请输入监控关键词"
            ></el-input>
            <div>
              <p style="font-size: 14px; margin-top: 20px">匹配模式</p>
              <el-checkbox-group v-model="rule.matchPattern" :max="1">
                <el-checkbox style="border-radius: 2px" label="完全匹配" :value="1" />
                <el-checkbox style="border-radius: 2px" label="模糊匹配" :value="2" />
              </el-checkbox-group>
            </div>
          </div>
          <div
            style="
              background-color: #f7f7f7;
              border-radius: 8px;
              padding: 25px 20px;
              margin-top: 20px;
            "
            @click="addRule(item)"
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                user-select: none;
              "
            >
              <img :src="Add" style="width: 15px; height: 15px; cursor: pointer" />
              <div>添加触发规则</div>
            </div>
          </div>
        </div>
        <div style="margin-top: 20px">
          <el-button
            color="#2072F7"
            style="
              width: 100%;
              color: #ffffff;
              font-weight: 700;
              border-radius: 8px;
              padding: 24px 10px !important;
            "
            type="primary"
            @click="addReply"
          >
            添加自动回复
          </el-button>
        </div>
      </div>
    </div>
    <div class="infos">
      <div class="item" @click="openSetInfo('')">
        <img :src="configSet.is_dark ? icon29_dark : icon29" alt="" />
      </div>
      <div :class="is_open_tab == 1 ? 'item on' : 'item'" @click="openSetInfo(1)">
        <img :src="is_open_tab == 1 ? icon30_1 : configSet.is_dark ? icon30_dark : icon30" alt="" />
        <p>翻译设置</p>
      </div>

      <div
        v-show="is_show_quick_remark"
        :class="is_open_tab == 2 ? 'item on' : 'item'"
        @click="openSetInfo(2)"
      >
        <img :src="is_open_tab == 2 ? icon31_1 : configSet.is_dark ? icon31_dark : icon31" alt="" />
        <p>快捷回复</p>
      </div>
      <div
        v-show="is_show_quick_remark"
        :class="is_open_tab == 3 ? 'item on' : 'item'"
        @click="openSetInfo(3)"
      >
        <img :src="is_open_tab == 3 ? icon32_1 : configSet.is_dark ? icon32_dark : icon32" alt="" />
        <p>粉丝备注</p>
      </div>
      <div
        v-show="is_show_quick_remark"
        :class="is_open_tab == 4 ? 'item on' : 'item'"
        @click="openSetInfo(4)"
      >
        <img :src="is_open_tab == 4 ? icon33_1 : configSet.is_dark ? icon33_dark : icon33" alt="" />
        <p>粉丝统计</p>
      </div>
      <div :class="is_open_tab == 5 ? 'item on' : 'item'" @click="toPage('/img_translate', '5')">
        <img :src="is_open_tab == 5 ? icon34_1 : configSet.is_dark ? icon34_dark : icon34" alt="" />
        <p>图片翻译</p>
      </div>
      <div :class="is_open_tab == 6 ? 'item on' : 'item'" @click="toPage('/coustome', '6')">
        <img :src="is_open_tab == 6 ? icon35_1 : configSet.is_dark ? icon35_dark : icon35" alt="" />
        <p>客户中心</p>
      </div>
      <!-- 隐藏 -->
      <!-- <div :class="is_open_tab == 7 ? 'item on' : 'item'" @click="openSetInfo(7)">
				<img :src="is_open_tab == 4 ? icon33_1 : (configSet.is_dark ? icon33_dark : icon33)" alt="">
				<p>自动回复</p>
			</div>
			<div :class="is_open_tab == 8 ? 'item on' : 'item'" @click="openSetInfo(8)">
				<img :src="is_open_tab == 4 ? icon33_1 : (configSet.is_dark ? icon33_dark : icon33)" alt="">
				<p>消息群发</p>
			</div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, toRaw } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getFansConfigSql,
  updateFansSql,
  getFansInfoSql,
  getFansRecordSql,
  getFansStatiasSql,
  getAllLangSql,
  getAllEngineSql,
  getQuickReplySql,
  setTranslateConfigrSql,
  getTranslateConfigSql
} from '../api/account.js'
// 定义和接收 props
const props = defineProps({
  softName: {
    type: String,
    required: true
  }
})
const configSet = ref({ is_dark: false }) // 配置信息
// 导入图片
const icon29 = new URL('../assets/img/icon29.svg', import.meta.url).href
const icon30 = new URL('../assets/img/icon30.svg', import.meta.url).href
const icon31 = new URL('../assets/img/icon31.svg', import.meta.url).href
const icon32 = new URL('../assets/img/icon32.svg', import.meta.url).href
const icon33 = new URL('../assets/img/icon33.svg', import.meta.url).href
const icon34 = new URL('../assets/img/icon34.svg', import.meta.url).href
const icon35 = new URL('../assets/img/icon35.svg', import.meta.url).href

const icon29_dark = new URL('../assets/img/dark/icon29.svg', import.meta.url).href
const icon30_dark = new URL('../assets/img/dark/icon30.svg', import.meta.url).href
const icon31_dark = new URL('../assets/img/dark/icon31.svg', import.meta.url).href
const icon32_dark = new URL('../assets/img/dark/icon32.svg', import.meta.url).href
const icon33_dark = new URL('../assets/img/dark/icon33.svg', import.meta.url).href
const icon34_dark = new URL('../assets/img/dark/icon34.svg', import.meta.url).href
const icon35_dark = new URL('../assets/img/dark/icon35.svg', import.meta.url).href

const icon30_1 = new URL('../assets/img/icon30-1.svg', import.meta.url).href
const icon31_1 = new URL('../assets/img/icon31-1.svg', import.meta.url).href
const icon32_1 = new URL('../assets/img/icon32-1.svg', import.meta.url).href
const icon33_1 = new URL('../assets/img/icon33-1.svg', import.meta.url).href
const icon34_1 = new URL('../assets/img/icon34-1.svg', import.meta.url).href
const icon35_1 = new URL('../assets/img/icon35-1.svg', import.meta.url).href

const icon41 = new URL('../assets/img/icon41.svg', import.meta.url).href
const icon39 = new URL('../assets/img/icon39.svg', import.meta.url).href
const icon40 = new URL('../assets/img/icon40.svg', import.meta.url).href
const icon42 = new URL('../assets/img/icon42.svg', import.meta.url).href
const icon43 = new URL('../assets/img/icon43.svg', import.meta.url).href
const icon42_1 = new URL('../assets/img/icon42-1.svg', import.meta.url).href
const icon43_1 = new URL('../assets/img/icon43-1.svg', import.meta.url).href
const icon44 = new URL('../assets/img/icon44.png', import.meta.url).href
const icon45 = new URL('../assets/img/icon45.svg', import.meta.url).href
import Delete from '../assets/img/Frame.svg'
import Add from '../assets/img/icon101.svg'
import { addNewFans } from '@renderer/api/account.js'
// 调用主页面的方法
const is_open = ref(false)
const is_open_tab = ref(0)
const emit = defineEmits(['openSetPage', 'translateSetParent', 'sendProloadJs'])
import { useRouter, useRoute } from 'vue-router' // 导入 useRouter
const router = useRouter() // 获取路由实例
const route = useRoute() // 获取当前路由对象
// 是否显示粉丝备注和快捷回复
const is_show_quick_remark = ref(false)
// 父级控制子级方法
const parent_top_son_show = () => {
  is_show_quick_remark.value = true
}
// 获取pinia中的翻译配置
import { debounce } from 'lodash-es'
import { useTabStore } from '../store/index.js'

const openSetInfo = (type) => {
  if (is_open.value) {
    is_open_tab.value = ''
    is_open.value = false
  } else {
    is_open.value = true
    if (type) {
      is_open_tab.value = type
    } else {
      is_open_tab.value = 1
    }
  }
  emit('openSetPage', is_open.value)

  // if (is_open.value || type == '' || type == 0) {
  //   is_open_tab.value = type
  //   is_open.value = !is_open.value
  //   emit('openSetPage', is_open.value)
  //   if (is_open.value) {
  //     is_open_tab.value = 1
  //   } else {
  //     is_open_tab.value = ''
  //   }
  // } else if (type !== '') {
  //   if (!is_open.value) {
  //     is_open.value = !is_open.value

  //   }

  // }
}
// 翻译信息设置======================
const translateSet = ref({})
// 设置翻译信息 传送给父类
const changeTranslateFun = () => {
  console.log(translateSet.value, 'translateSet')
  saveTransConfigToGlobal()
  // setTranslateConfigrSql({
  //   engineCode: translateSet.value.engineCode ? translateSet.value.engineCode : 'googlehk',
  //   recive_lang: translateSet.value.recive_lang,
  //   gust_lang: translateSet.value.gust_lang,
  //   send_lang: translateSet.value.send_lang,
  //   self: translateSet.value.self,
  //   trans_over: translateSet.value.trans_over,
  //   self_many: translateSet.value.self_many,
  //   login_trans_his: translateSet.value.login_trans_his,
  //   send_trans_zh: translateSet.value.send_trans_zh
  // }).then((response) => {
  //   if (response.code == 1) {
  //     // emit('translateSetParent', translateSet.value)

  //   }
  // })
}
// 清空发来消息翻译
const clearTransConfig = (res) => {
  translateSet.value = {}
  // setTranslateConfigrSql({
  //   engineCode: translateSet.value.engineCode ? translateSet.value.engineCode : 'googlehk',
  //   recive_lang: '',
  //   gust_lang: translateSet.value.gust_lang,
  //   send_lang: translateSet.value.send_lang,
  //   self: translateSet.value.self,
  //   trans_over: translateSet.value.trans_over,
  //   self_many: translateSet.value.self_many,
  //   login_trans_his: translateSet.value.login_trans_his,
  //   send_trans_zh: translateSet.value.send_trans_zh
  // }).then((response) => {
  //   saveTransConfigToGlobal()
  // })
}
// 将翻译配置存入全局状态管理
const saveTransConfigToGlobal = () => {
  // 存入主进程
  let config = {
    engineCode: translateSet.value.engineCode,
    recive_lang: translateSet.value.recive_lang,
    send_lang: translateSet.value.send_lang,
    self: translateSet.value.self,
    trans_over: translateSet.value.trans_over,
    self_many: translateSet.value.self_many
  }
  window.electron.ipcRenderer.send('updateTranslateConfig', {
    config,
    sessionId: tabarStore.getCurrentSessionUserId(),
    isChating: route.name === 'chat'
  })
}
// 设置发送方式--快捷回复的发送sendProloadJs
const sendClickFun = (type, message) => {
  emit('sendProloadJs', type, message, translateSet.value, sendType.value)
}
// 发送方式
const sendType = ref(1)
const changeSendType = (type) => {
  sendType.value = type
}
// 性别设置-------==========
const sex = ref(1)
const cahngeSex = (type) => {
  fansInfo.value.fans_sex = type
  sex.value = type
}
// 查询用户详细信息====================
const fansInfo = ref({})

function getFansInfo(mainToFansInfo) {
  console.log(mainToFansInfo)
  if (mainToFansInfo) {
    fansInfo.value = mainToFansInfo
    sex.value = fansInfo.value.fans_sex
  }
}
// 获取粉丝配置fansConfig
const fansConfig = ref({})

function getFansConfig() {
  getFansConfigSql({}).then((response) => {
    if (response.code == 1) {
      fansConfig.value = response.data
    }
  })
}
getFansConfig()
// 更新粉丝信息
const fansUpdate = ref({})

const formData = ref()
const addCacheFansInfo = (d) => {
  formData.value = d
}

// 添加新粉丝
const addFans = async (formData) => {
  await addNewFans(formData)

  const response = await getFansInfoSql(formData)
  if (response.code == 1 && response.data.id) {
    return response.data.id
    // response.data.fans_level = response.data.fans_level > 0 ? String(response.data.fans_level) : ''
    // response.data.order_stage = response.data.order_stage > 0 ? String(response.data.order_stage) : ''
    // response.data.sales_stage = response.data.sales_stage > 0 ? String(response.data.sales_stage) : ''
    // response.data.fans_sources = response.data.fans_sources > 0 ? String(response.data.fans_sources) : ''
    // response.data.fans_name = response.data.fans_account_name
  }
}

const updateFansInfo = debounce(async function updateFansInfo() {
  if (!fansInfo.value.fans_name) {
    ElMessage.warning({
      message: '请输入客户名'
    })
    return
  }
  if (!fansInfo.value.fans_mobile) {
    ElMessage.warning({
      message: '请输入电话'
    })
    return
  }

  if (!fansInfo.value.fans_level) {
    ElMessage.warning({
      message: '请选择客户等级'
    })
    return
  }
  if (!fansInfo.value.order_stage) {
    ElMessage.warning({
      message: '请选择订单阶段'
    })
    return
  }
  if (!fansInfo.value.sales_stage) {
    ElMessage.warning({
      message: '请选择销售阶段'
    })
    return
  }
  if (!fansInfo.value.company) {
    ElMessage.warning({
      message: '请输入公司名称'
    })
    return
  }

  if (!fansInfo.value.id) {
    fansInfo.value.id = await addFans(formData.value)
  }
  fansUpdate.value = {
    fans_id: fansInfo.value.id,
    is_customer: fansInfo.value.is_customer,
    fans_name: fansInfo.value.fans_name,
    fans_sex: fansInfo.value.fans_sex,
    fans_mobile: fansInfo.value.fans_mobile,
    country: fansInfo.value.country,
    fans_level: fansInfo.value.fans_level,
    order_stage: fansInfo.value.order_stage,
    sales_stage: fansInfo.value.sales_stage,
    fans_sources: fansInfo.value.fans_sources,
    company: fansInfo.value.company,
    position: fansInfo.value.position,
    remark: fansInfo.value.remark
  }
  updateFansSql(fansUpdate.value).then((response) => {
    ElMessage({
      showClose: true,
      message: response.msg,
      type: response.code == 1 ? 'success' : 'info'
    })
  })
}, 500)

// 根据主账号获取粉丝数据
const fansRecord = ref({})

function getFansRecord(mainAccount) {
  let formData = {
    userId: mainAccount
  }
  getFansRecordSql(formData).then((response) => {
    if (response.code == 1) {
      fansRecord.value = response.data
    }
  })
}

// 根据工单号获取粉丝数据
const fansStatias = ref({})

function getFansStatias(order_code) {
  let formData = {
    orderNumber: order_code
  }
  getFansStatiasSql(formData).then((response) => {
    if (response.code == 1) {
      fansStatias.value = response.data
    }
  })
}

// 获取所有语言
const allLang = ref({})
const allLangSetInterval = ref(null)
function getAllLang() {
  getAllLangSql({}).then((response) => {
    if (response.code == 1) {
      allLang.value = response.data
      clearInterval(allLangSetInterval.value)
    }
  })
}
getAllLang()

// setTimeout(function () {
//   if (allLang.value == {}) {
//     allLangSetInterval.value = setInterval(function () {
//       getAllLang()
//       // 获取去翻译设置信息

//     }, 5000)
//   }
// }, 3000)

const tabarStore = useTabStore()
const getConfig = async () => {
  translateSet.value = await window.electron.ipcRenderer.invoke('getTranslateConfig', {
    sessionId: tabarStore.getCurrentSessionUserId(),
    isChating: route.name === 'chat'
  })

  // getTranslateConfigSql({}).then((response) => {
  //     if (response.data) {
  //       translateSet.value = response.data
  //       if (
  //         translateSet.value &&
  //         !(translateSet.value.recive_lang && translateSet.value.send_lang)
  //       ) {

  //         setTranslateConfigrSql({
  //           engineCode: translateSet.value.engineCode ? translateSet.value.engineCode : 'googlehk',
  //           recive_lang: translateSet.value.recive_lang,
  //           gust_lang: translateSet.value.gust_lang,
  //           send_lang: translateSet.value.send_lang,
  //           self: translateSet.value.self,
  //           trans_over: translateSet.value.trans_over,
  //           self_many: translateSet.value.self_many,
  //           login_trans_his: translateSet.value.login_trans_his,
  //           send_trans_zh: translateSet.value.send_trans_zh
  //         }).then((response) => {
  //           if (response.code == 1) {
  //             emit('translateSetParent', translateSet.value)
  //             saveTransConfigToGlobal()
  //           }
  //         })
  //       }
  //       saveTransConfigToGlobal()
  //       clearInterval(allLangSetInterval.value)
  //     }
  //   })
}

// 获取所有翻译引擎
const allEngine = ref({})

function getAllEngine() {
  getAllEngineSql({}).then((response) => {
    if (response.code == 1) {
      allEngine.value = response.data
    }
  })
}
getAllEngine()

// 获取公共快捷回复
const quickReply = ref([])

function getQuickReply() {
  getQuickReplySql({}).then((response) => {
    if (response.code == 1) {
      quickReply.value = response.data
    }
  })
}
// 点击展开快捷回复
const showOrCloseFun = (index, sonIndex) => {
  if (sonIndex == -1) {
    quickReply.value[index].is_sel = !quickReply.value[index].is_sel
  } else {
    quickReply.value[index].quickReply[sonIndex].is_sel =
      !quickReply.value[index].quickReply[sonIndex].is_sel
  }
}
// 快捷回复
const quickType = ref(1)
const changeQuickType = (type) => {
  quickType.value = type
  if (type == 1) {
    quickReply.value = []
    if (localStorage.getItem('selfQuick')) {
      quickReply.value = JSON.parse(localStorage.getItem('selfQuick'))
    }
  } else {
    getQuickReply()
  }
}

// 搜索框输入
const searchName = ref('')
const searchInput = () => {
  let over_q_arr = []
  if (quickType.value == 1) {
    // 个人快捷回复
    let quick_list = JSON.parse(localStorage.getItem('selfQuick'))
    for (let i = 0; i < quick_list.length; i++) {
      let quick_arr = []
      if (quick_list[i].quickReply) {
        for (let j = 0; j < quick_list[i].quickReply.length; j++) {
          // if(quick_list[i].quickReply[j].name.indexOf(searchName.value) !== -1){
          // 	quick_arr.push(quick_list[i].quickReply[j])
          // }
          // 查询内容
          let text_arr = []
          for (let n = 0; n < quick_list[i].quickReply[j].text.length; n++) {
            if (quick_list[i].quickReply[j].text[n].message.indexOf(searchName.value) !== -1) {
              text_arr.push(quick_list[i].quickReply[j].text[n])
            }
          }
          if (text_arr.length > 0) {
            quick_list[i].quickReply[j].text = text_arr
            quick_list[i].quickReply[j].is_sel = true
            quick_arr.push(quick_list[i].quickReply[j])
          }
        }
        if (quick_arr.length > 0) {
          quick_list[i].quickReply = quick_arr
          quick_list[i].is_sel = true
          over_q_arr.push(quick_list[i])
        }
      }
    }
    if (searchName.value == '' || !searchName.value) {
      quickReply.value = JSON.parse(localStorage.getItem('selfQuick'))
    } else {
      quickReply.value = over_q_arr
    }
  } else {
    // 公共快捷回复
    let quick_list = quickReply.value
    for (let i = 0; i < quick_list.length; i++) {
      let quick_arr = []
      if (quick_list[i].quickReply) {
        for (let j = 0; j < quick_list[i].quickReply.length; j++) {
          // if(quick_list[i].quickReply[j].name.indexOf(searchName.value) !== -1){
          // 	quick_arr.push(quick_list[i].quickReply[j])
          // }
          let text_arr = []
          for (let n = 0; n < quick_list[i].quickReply[j].text.length; n++) {
            if (quick_list[i].quickReply[j].text[n].indexOf(searchName.value) !== -1) {
              text_arr.push(quick_list[i].quickReply[j].text[n])
            }
          }
          if (text_arr.length > 0) {
            quick_list[i].quickReply[j].text = text_arr
            quick_list[i].quickReply[j].is_sel = true
            quick_arr.push(quick_list[i].quickReply[j])
          }
        }
        if (quick_arr.length > 0) {
          quick_list[i].quickReply = quick_arr
          quick_list[i].is_sel = true
          over_q_arr.push(quick_list[i])
        }
      }
    }
    if (searchName.value == '' || !searchName.value) {
      getQuickReply()
    } else {
      quickReply.value = over_q_arr
    }
  }
}

// 自动回复
// {
// 	isAutoReply: boolean;
// 	autoReplySetList: {
// 		replyContent: string;
// 		isSyncMonitoringGroup: boolean;
// 		monitoringKeywords: {
// 			keywords: string;
// 			matchPattern: number; // 1 完全匹配 2模糊匹配
// 		} [];
// 	} [];
// }
window.electron.ipcRenderer.on('onRecivedMessage', (data) => {
  if (autoReplySetInfo.isAutoReply) {
    const messageData = JSON.parse(data)
    // TODO 需要区分群消息还是好友消息
    autoReplySetInfo.autoReplySetList.forEach((item) => {
      // if (!item.isSyncMonitoringGroup) {
      // }
      item.monitoringKeywords.forEach((keywordsItem) => {
        switch (keywordsItem.matchPattern) {
          case 1:
            if (messageData.text === keywordsItem.keywords) {
              emit('sendProloadJs', type, item.replyContent, translateSet.value)
            }

            break
          case 2:
            if (keywordsItem.keywords.match(messageData.text)) {
              // TODO 匹配上需要根据语言做翻译 然后传给容器返给好友
              emit('sendProloadJs', type, item.replyContent, translateSet.value)
            }
            break
        }
      })
    })
  }
})

const autoReplySetInfo = reactive({
  isAutoReply: false,
  autoReplySetList: [
    // {
    // 	replyContent: '',
    // 	isSyncMonitoringGroupGroup: false,
    // 	monitoringKeywords: [
    // 		{
    // 			keywords: '',
    // 			matchPattern: 1,
    // 		}
    // 	]
    // }
  ]
})
// 添加自动回复
const addReply = () => {
  autoReplySetInfo.autoReplySetList.push({
    replyContent: '',
    isSyncMonitoringGroup: false,
    monitoringKeywords: [
      {
        keywords: '',
        matchPattern: [1]
      }
    ]
  })
}
// 添加触发规则
const addRule = (item) => {
  // autoReplySetInfo.autoReplySetList
  item.monitoringKeywords.push({
    keywords: '',
    matchPattern: [1]
  })
}
const deleteRule = (item, index) => {
  item.splice(index, 1)
}

const deleteReply = (index) => {
  autoReplySetInfo.autoReplySetList.splice(index, 1)
}

// 暴露方法给父组件
defineExpose({
  addCacheFansInfo, // 添加新增粉丝的数据
  getFansInfo, // 获取粉丝详情
  getFansRecord, // 当前主账号粉丝统计
  parent_top_son_show, // 显示粉丝备注和快捷回复
  clearTransConfig, // 清楚翻译设置信息
  autoReplySetInfo, // 自动回复信息对象
  getFansStatias // 工单
})

// 接收参数
const now_path = ref(null)
onMounted(() => {
  is_open_tab.value = route.query.param
  now_path.value = route.path
  if (localStorage.getItem('configSet')) {
    configSet.value = JSON.parse(localStorage.getItem('configSet'))
  }

  getConfig()
  // // 本地存储翻译配置信息
  // if (localStorage.getItem('translateSet')) {
  //   debugger
  // 	translateSet.value = JSON.parse(localStorage.getItem('translateSet'))
  // }

  // 初始化翻译设置信息
  // getTranslateConfigSql({}).then((response) => {
  //   if (response.data) {
  //     translateSet.value = response.data
  //     translateSet.value.send_trans_zh = false
  //     saveTransConfigToGlobal()
  //   }
  // })

  // 默认获取个人快捷回复信息
  if (localStorage.getItem('selfQuick')) {
    quickReply.value = JSON.parse(localStorage.getItem('selfQuick'))
  }
})
// 跳转页面
const toPage = (url, param) => {
  if (now_path.value == '/img_translate' && param == 5) {
    is_open_tab.value = 5
    is_open.value = false
    emit('openSetPage', is_open.value)
  }
  if (now_path.value == '/coustome' && param == 6) {
    is_open_tab.value = 6
    is_open.value = false
    emit('openSetPage', is_open.value)
  } else {
    router.push({
      path: url,
      query: { param: param }
    })
  }
}
</script>

<style lang="scss" scoped>
@import url(../assets/style/userset.scss);

.switch_line {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.open_auto_reply,
.close_auto_reply {
  border: 1.12px solid;
  padding: 1px 8px;
  font-size: 10px;
  border-radius: 3px;
  line-height: 15px;
}

.open_auto_reply {
  color: #54b85b;
}

.close_auto_reply {
  color: orange;
}

.tips {
  margin-top: 10px;
  line-height: 20px;
  color: #a7a7a7;
  font-size: 10px;
  margin-bottom: 30px;
}
</style>
